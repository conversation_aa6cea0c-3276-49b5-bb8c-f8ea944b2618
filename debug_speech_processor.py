#!/usr/bin/env python3

import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_model_download():
    """Test the model download functionality"""
    try:
        from speech_processor import SpeechProcessor
        
        print("Testing SpeechProcessor initialization...")
        processor = SpeechProcessor()
        print("SpeechProcessor initialized successfully!")
        
    except Exception as e:
        print(f"Error during initialization: {e}")
        import traceback
        traceback.print_exc()

def test_model_path():
    """Test model path directly"""
    try:
        from huggingface_hub import snapshot_download
        
        model_name = "Systran/faster-distil-whisper-large-v3"
        local_cache_dir = Path("./model_cache")
        local_cache_dir.mkdir(exist_ok=True)
        
        print(f"Downloading {model_name}...")
        downloaded_path = snapshot_download(
            repo_id=model_name,
            cache_dir=str(local_cache_dir),
            local_files_only=False,
            revision="main"
        )
        
        print(f"Downloaded to: {downloaded_path}")
        print(f"Path type: {type(downloaded_path)}")
        print(f"Path exists: {Path(downloaded_path).exists()}")
        
        # Check model.bin
        model_bin_path = Path(downloaded_path) / "model.bin"
        print(f"model.bin exists: {model_bin_path.exists()}")
        print(f"model.bin path: {model_bin_path}")
        
        return downloaded_path
        
    except Exception as e:
        print(f"Error during model download test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_ctranslate2():
    """Test CTranslate2 model loading"""
    try:
        import ctranslate2
        
        model_path = test_model_path()
        if model_path is None:
            print("Model path is None, cannot test CTranslate2")
            return
            
        print(f"Testing CTranslate2 with path: {model_path}")
        
        # Try to initialize CTranslate2 model
        model = ctranslate2.models.Whisper(
            model_path,
            device="cuda" if torch.cuda.is_available() else "cpu",
            compute_type="float16"
        )
        
        print("CTranslate2 model loaded successfully!")
        
    except Exception as e:
        print(f"Error during CTranslate2 test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=== Debugging SpeechProcessor ===")
    
    print("\n1. Testing model download...")
    test_model_path()
    
    print("\n2. Testing CTranslate2...")
    import torch
    test_ctranslate2()
    
    print("\n3. Testing full SpeechProcessor...")
    test_model_download()
