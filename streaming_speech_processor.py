import sounddevice as sd
import numpy as np
import torch
import time
import threading
import queue
import logging
from collections import deque
import ctranslate2
import noisereduce as nr
import scipy.signal
from transformers import AutoProcessor

logger = logging.getLogger(__name__)

class StreamingSpeechProcessor:
    """
    Streaming Speech-to-Text processor using CTranslate2 with Systran/faster-distil-whisper-large-v3
    Optimized for real-time transcription on NVIDIA GTX 1650 GPU
    """

    def __init__(self, speech_processor):
        """Initialize streaming processor using existing speech processor"""
        self.base_processor = speech_processor
        self.model = speech_processor.model
        self.processor = speech_processor.processor  # May be None for faster-whisper
        self.sample_rate = speech_processor.sample_rate
        self.use_faster_whisper = getattr(speech_processor, 'use_faster_whisper', False)
        
        # Streaming configuration - optimized for real-time
        self.chunk_duration = 1.5  # Reduced for faster response
        self.overlap_duration = 0.3  # Reduced overlap for speed
        self.chunk_samples = int(self.chunk_duration * self.sample_rate)
        self.overlap_samples = int(self.overlap_duration * self.sample_rate)
        
        # Audio buffer and processing
        self.audio_buffer = deque(maxlen=int(10 * self.sample_rate))  # 10 second buffer
        self.processing_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # Threading control
        self.is_recording = False
        self.is_processing = False
        self.recording_thread = None
        self.processing_thread = None
        
        # Audio stream
        self.audio_stream = None
        
        # CTranslate2 transcription parameters optimized for real-time streaming
        self.streaming_params = {
            'beam_size': 2,  # Balanced for GTX 1650 speed and accuracy
            'best_of': 2,
            'patience': 1.0,
            'temperature': 0.0,  # Deterministic for consistency
            'length_penalty': 1.0,
            'repetition_penalty': 1.05,
            'no_repeat_ngram_size': 2,
            'compression_ratio_threshold': 2.2,
            'log_prob_threshold': -0.8,
            'no_speech_threshold': 0.4,  # Lower for better detection
            'condition_on_previous_text': True,  # Better context
            'suppress_blank': True,
            'suppress_tokens': [-1],
            'initial_prompt': 'This is clear English speech with proper grammar.',
            'word_timestamps': False,  # Faster without word timestamps
            'max_initial_timestamp': 0.5,
        }
        
        logger.info("Streaming speech processor initialized")
    
    def audio_callback(self, indata, frames, time_info, status):
        """Callback for audio stream"""
        if status:
            if 'input overflow' not in str(status).lower():  # Ignore common overflow warnings
                logger.warning(f"Audio callback status: {status}")

        if self.is_recording:
            # Convert to mono and ensure float32
            audio_data = indata[:, 0] if indata.ndim > 1 else indata
            audio_data = audio_data.flatten().astype(np.float32)
            self.audio_buffer.extend(audio_data)
    
    def start_streaming(self, callback=None):
        """Start streaming audio capture and processing"""
        if self.is_recording:
            logger.warning("Already recording")
            return False
        
        try:
            self.is_recording = True
            self.is_processing = True
            self.callback = callback
            
            # Clear buffers
            self.audio_buffer.clear()
            while not self.processing_queue.empty():
                self.processing_queue.get()
            while not self.result_queue.empty():
                self.result_queue.get()
            
            # Start audio stream
            self.audio_stream = sd.InputStream(
                samplerate=self.sample_rate,
                channels=1,
                dtype='float32',
                callback=self.audio_callback,
                blocksize=1024,
                latency='low'
            )
            self.audio_stream.start()
            
            # Start processing thread
            self.processing_thread = threading.Thread(target=self._processing_loop)
            self.processing_thread.daemon = True
            self.processing_thread.start()
            
            # Start result handling thread
            self.result_thread = threading.Thread(target=self._result_loop)
            self.result_thread.daemon = True
            self.result_thread.start()
            
            logger.info("Streaming started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start streaming: {e}")
            self.stop_streaming()
            return False
    
    def stop_streaming(self):
        """Stop streaming audio capture and processing"""
        logger.info("Stopping streaming...")
        
        self.is_recording = False
        self.is_processing = False
        
        # Stop audio stream
        if self.audio_stream:
            try:
                self.audio_stream.stop()
                self.audio_stream.close()
            except Exception as e:
                logger.error(f"Error stopping audio stream: {e}")
            self.audio_stream = None
        
        # Wait for threads to finish
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2)
        
        if hasattr(self, 'result_thread') and self.result_thread.is_alive():
            self.result_thread.join(timeout=2)
        
        logger.info("Streaming stopped")
    
    def _processing_loop(self):
        """Main processing loop for audio chunks - optimized for real-time"""
        last_process_time = 0
        processed_samples = 0

        while self.is_processing:
            try:
                current_time = time.time()

                # Process more frequently for real-time response
                process_interval = self.chunk_duration * 0.7  # Process 30% earlier

                if current_time - last_process_time >= process_interval:
                    buffer_length = len(self.audio_buffer)

                    if buffer_length >= self.chunk_samples:
                        # Extract chunk with overlap
                        start_idx = max(0, processed_samples - self.overlap_samples)
                        end_idx = min(buffer_length, start_idx + self.chunk_samples)

                        # Reduced minimum chunk size for faster response
                        min_chunk_size = int(self.sample_rate * 0.8)  # 0.8 seconds minimum

                        if end_idx - start_idx >= min_chunk_size:
                            # Convert deque to numpy array for the chunk
                            chunk_data = np.array(list(self.audio_buffer)[start_idx:end_idx], dtype=np.float32)

                            # Process the chunk
                            self._process_audio_chunk(chunk_data, processed_samples)

                            processed_samples = end_idx - self.overlap_samples

                    last_process_time = current_time

                time.sleep(0.05)  # Reduced delay for faster response

            except Exception as e:
                logger.error(f"Error in processing loop: {e}")
                time.sleep(0.2)  # Reduced error delay
    
    def _process_audio_chunk(self, audio_chunk, chunk_index):
        """Process a single audio chunk using faster-whisper or CTranslate2 for real-time streaming"""
        try:
            # Ensure audio is float32 for compatibility
            if audio_chunk.dtype != np.float32:
                audio_chunk = audio_chunk.astype(np.float32)

            # Optimized preprocessing for streaming
            # Remove DC offset
            audio_chunk = (audio_chunk - np.mean(audio_chunk)).astype(np.float32)

            # Normalize audio
            max_val = np.max(np.abs(audio_chunk))
            if max_val > 0:
                audio_chunk = (audio_chunk / max_val * 0.8).astype(np.float32)

            # Ensure minimum length for reliable transcription
            if len(audio_chunk) < self.sample_rate * 0.8:  # Less than 0.8 seconds
                return

            # Voice activity detection - check if there's enough energy
            rms = np.sqrt(np.mean(np.square(audio_chunk)))
            if rms < 0.01:  # Too quiet
                return

            logger.info(f"Processing: dtype={audio_chunk.dtype}, shape={audio_chunk.shape}, rms={rms:.4f}")

            if self.use_faster_whisper:
                # Use faster-whisper for transcription
                self._transcribe_with_faster_whisper(audio_chunk, chunk_index)
            else:
                # Use CTranslate2 for transcription
                self._transcribe_with_ctranslate2(audio_chunk, chunk_index)

        except Exception as e:
            logger.error(f"Error processing audio chunk: {e}")

    def _transcribe_with_faster_whisper(self, audio_chunk, chunk_index):
        """Transcribe using faster-whisper"""
        try:
            # faster-whisper transcription with streaming-optimized parameters
            segments, info = self.model.transcribe(
                audio_chunk,
                beam_size=1,  # Fast greedy decoding for streaming
                best_of=1,
                temperature=0.0,
                compression_ratio_threshold=2.4,
                log_prob_threshold=-1.0,
                no_speech_threshold=0.6,
                condition_on_previous_text=False,  # Disable for streaming consistency
                word_timestamps=False,  # Disable for speed
                vad_filter=True,
                vad_parameters=dict(min_silence_duration_ms=500),
            )

            # Combine segments
            transcription = ""
            for segment in segments:
                transcription += segment.text + " "

            transcription = transcription.strip()

            if transcription and len(transcription) > 2:
                # Post-process and emit result
                processed_text = self.base_processor._post_process_text(transcription)
                if processed_text and len(processed_text) > 2:
                    self.result_queue.put({
                        'text': processed_text,
                        'chunk_index': chunk_index,
                        'confidence': getattr(info, 'language_probability', 0.8),
                        'timestamp': time.time()
                    })

        except Exception as e:
            logger.error(f"faster-whisper transcription failed: {e}")

    def _transcribe_with_ctranslate2(self, audio_chunk, chunk_index):
        """Transcribe using CTranslate2 (fallback method)"""
        try:
            # Extract features using the processor
            if self.processor is None:
                logger.error("CTranslate2 processor not available")
                return

            features = self.processor(
                audio_chunk,
                sampling_rate=self.sample_rate,
                return_tensors="np"
            )
            input_features = features.input_features[0]

            # CTranslate2 transcription with streaming-optimized parameters
            results = self.model.transcribe(
                input_features,
                beam_size=1,  # Fast greedy decoding for streaming
                best_of=1,
                temperature=0.0,
                length_penalty=1.0,
                repetition_penalty=1.0,
                no_repeat_ngram_size=0,
                compression_ratio_threshold=2.4,
                log_prob_threshold=-1.0,
                no_speech_threshold=0.6,
                condition_on_previous_text=False,  # Disable for streaming consistency
                suppress_blank=True,
                suppress_tokens=[-1],
            )

            if results and len(results) > 0:
                # Extract text from CTranslate2 results
                text = ""
                confidence = 0.5  # Default confidence

                for result in results:
                    if hasattr(result, 'text'):
                        text += result.text
                    elif isinstance(result, dict) and 'text' in result:
                        text += result['text']

                    # Extract confidence if available
                    if hasattr(result, 'avg_logprob') and result.avg_logprob is not None:
                        confidence = min(np.exp(result.avg_logprob), 1.0)
                    elif isinstance(result, dict) and 'avg_logprob' in result:
                        confidence = min(np.exp(result['avg_logprob']), 1.0)

                text = text.strip()
                if text:
                    # Clean transcription
                    cleaned_text = self.base_processor._post_process_text(text)

                    if cleaned_text and len(cleaned_text) > 1:  # Reduced threshold for faster response
                        # Add to result queue
                        self.result_queue.put({
                            'text': cleaned_text,
                            'chunk_index': chunk_index,
                            'timestamp': time.time(),
                            'confidence': confidence
                        })
                        logger.info(f"CTranslate2 transcribed: '{cleaned_text}' (confidence: {confidence:.2f})")

        except Exception as e:
            logger.error(f"Error processing audio chunk with CTranslate2: {e}")
    
    def _result_loop(self):
        """Handle transcription results"""
        while self.is_processing:
            try:
                # Get result with timeout
                result = self.result_queue.get(timeout=1.0)
                
                if self.callback:
                    self.callback(result)
                    
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in result loop: {e}")
    
    def get_audio_level(self):
        """Get current audio level for UI feedback"""
        if len(self.audio_buffer) > 0:
            recent_samples = min(1024, len(self.audio_buffer))
            recent_audio = np.array(list(self.audio_buffer)[-recent_samples:])
            return float(np.max(np.abs(recent_audio)))
        return 0.0
    
    def is_active(self):
        """Check if streaming is active"""
        return self.is_recording and self.audio_stream is not None
